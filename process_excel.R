# Excel文件处理脚本
# 功能：处理质谱数据，计算中性丢失、相对强度和熵值

# 加载必要的包
library(readxl)
library(dplyr)
library(writexl)

# 设置工作目录
setwd("C:/Users/<USER>/Desktop/test")

# 创建输出文件夹
output_dir <- "output"
if (!dir.exists(output_dir)) {
  dir.create(output_dir)
}

# 处理单个Excel文件的函数
process_excel_file <- function(file_path) {
  
  # 获取文件名（不含路径和扩展名）
  file_name <- tools::file_path_sans_ext(basename(file_path))
  
  # 从文件名中提取母离子质量（第二个下划线后的数值）
  # 例如：1_60_375.29.xlsx -> 375.29
  parts <- strsplit(file_name, "_")[[1]]
  if (length(parts) < 3) {
    stop(paste("文件名格式不正确:", file_name, "应包含至少两个下划线"))
  }
  
  # 提取母离子质量
  parent_ion_str <- parts[3]
  parent_ion <- as.numeric(parent_ion_str)
  
  if (is.na(parent_ion)) {
    stop(paste("无法从文件名中提取母离子质量:", parent_ion_str))
  }
  
  cat("处理文件:", file_name, "\n")
  cat("母离子质量:", parent_ion, "\n")
  
  # 读取Excel文件
  data <- read_excel(file_path)

  # 检查必要的列是否存在
  if (!"mz" %in% colnames(data)) {
    stop("Excel文件中缺少'mz'列")
  }
  if (!"intensity" %in% colnames(data)) {
    stop("Excel文件中缺少'intensity'列")
  }

  # 转换数据类型（处理可能的字符型数据）
  data$mz <- as.numeric(data$mz)
  data$intensity <- as.numeric(data$intensity)

  # 检查转换后是否有NA值
  if (any(is.na(data$mz))) {
    cat("警告: mz列中有无法转换的值\n")
  }
  if (any(is.na(data$intensity))) {
    cat("警告: intensity列中有无法转换的值\n")
  }
  
  cat("原始数据行数:", nrow(data), "\n")
  
  # 1. 按mz列降序排列
  data <- data %>% arrange(desc(mz))
  
  # 2. 删除mz > 母离子+0.005的行
  threshold <- parent_ion + 0.005
  data <- data %>% filter(mz <= threshold)
  
  cat("过滤后数据行数:", nrow(data), "\n")
  
  # 3. 保留前60行（如果少于60行则全部保留）
  if (nrow(data) > 60) {
    data <- data %>% slice_head(n = 60)
    cat("保留前60行\n")
  } else {
    cat("数据少于60行，全部保留\n")
  }
  
  # 4. 计算中性丢失（母离子 - mz）
  data <- data %>% mutate(Loss_Neutral = parent_ion - mz)
  
  # 5. 计算相对强度
  total_intensity <- sum(data$intensity, na.rm = TRUE)
  data <- data %>% mutate(relative_intensity = intensity / total_intensity)
  
  # 6. 计算熵值 E = -Sum(相对强度 * log(相对强度))
  # 注意：当相对强度为0时，log(0)为-Inf，需要特殊处理
  # 使用自然对数ln，如果需要log10可以修改
  valid_rel_int <- data$relative_intensity[data$relative_intensity > 0]
  if (length(valid_rel_int) > 0) {
    E_value <- -sum(valid_rel_int * log(valid_rel_int))
  } else {
    E_value <- 0
  }
  
  cat("计算得到的熵值 E:", E_value, "\n")
  
  # 7. 输出到新文件
  # 将E值四舍五入到4位小数，避免文件名过长
  E_rounded <- round(E_value, 4)
  output_file_name <- paste0(file_name, "_", E_rounded, ".xlsx")
  output_file_path <- file.path(output_dir, output_file_name)
  
  # 不添加Entropy_E列，熵值仅用于文件命名
  
  # 保存处理后的数据
  write_xlsx(data, output_file_path)
  
  cat("文件已保存到:", output_file_path, "\n")
  cat("最终数据行数:", nrow(data), "\n")
  cat("最终数据列数:", ncol(data), "\n")
  cat("列名:", paste(colnames(data), collapse = ", "), "\n")
  cat("----------------------------------------\n")
  
  return(list(
    file_name = file_name,
    parent_ion = parent_ion,
    final_rows = nrow(data),
    entropy = E_value,
    output_path = output_file_path
  ))
}

# 主程序：处理input文件夹中的所有Excel文件
input_dir <- "input"
excel_files <- list.files(input_dir, pattern = "\\.xlsx?$", full.names = TRUE)

if (length(excel_files) == 0) {
  cat("在input文件夹中没有找到Excel文件\n")
} else {
  cat("找到", length(excel_files), "个Excel文件\n")
  
  # 存储处理结果
  results <- list()
  
  # 处理每个文件
  for (file_path in excel_files) {
    tryCatch({
      result <- process_excel_file(file_path)
      results[[length(results) + 1]] <- result
    }, error = function(e) {
      cat("处理文件", file_path, "时出错:", e$message, "\n")
    })
  }
  
  # 输出处理摘要
  cat("\n=== 处理摘要 ===\n")
  for (i in seq_along(results)) {
    result <- results[[i]]
    cat("文件:", result$file_name, "\n")
    cat("  母离子:", result$parent_ion, "\n")
    cat("  最终行数:", result$final_rows, "\n")
    cat("  熵值 E:", round(result$entropy, 6), "\n")
    cat("  输出文件:", result$output_path, "\n\n")
  }
}

cat("脚本执行完成！\n")
