# 加载必要的包
library(readxl)
library(dplyr)
library(writexl)
library(parallel)
library(foreach)
library(doParallel)

# 处理单个Excel文件的函数
process_excel_file <- function(file_path, output_dir) {
  
  # 获取文件名（不含路径和扩展名）
  file_name <- tools::file_path_sans_ext(basename(file_path))
  
  # 从文件名中提取母离子质量（第二个下划线后的数值）
  parts <- strsplit(file_name, "_")[[1]]
  if (length(parts) < 3) {
    stop(paste("文件名格式不正确:", file_name, "应包含至少两个下划线"))
  }
  
  # 提取母离子质量
  parent_ion_str <- parts[3]
  parent_ion <- as.numeric(parent_ion_str)
  
  if (is.na(parent_ion)) {
    stop(paste("无法从文件名中提取母离子质量:", parent_ion_str))
  }
  
  # 获取所有sheet名称
  all_sheets <- excel_sheets(file_path)

  # 读取第一个sheet进行处理
  data <- read_excel(file_path, sheet = 1)

  # 检查必要的列是否存在
  if (!"mz" %in% colnames(data)) {
    stop("Excel文件中缺少'mz'列")
  }
  if (!"intensity" %in% colnames(data)) {
    stop("Excel文件中缺少'intensity'列")
  }

  # 转换数据类型（处理可能的字符型数据）
  data$mz <- as.numeric(data$mz)
  data$intensity <- as.numeric(data$intensity)
  
  # 检查转换后是否有NA值
  if (any(is.na(data$mz))) {
    warning("mz列中有无法转换的值")
  }
  if (any(is.na(data$intensity))) {
    warning("intensity列中有无法转换的值")
  }
  
  original_rows <- nrow(data)
  
  # 1. 按mz列降序排列
  data <- data %>% arrange(desc(mz))

  # 2. 删除大于母离子+0.005的行（允许0.005误差范围）
  threshold <- parent_ion + 0.005
  data <- data %>% filter(mz <= threshold)
  
  filtered_rows <- nrow(data)
  
  # 3. 保留前60行（如果少于60行则全部保留）
  if (nrow(data) > 60) {
    data <- data %>% slice_head(n = 60)
    final_rows <- 60
  } else {
    final_rows <- nrow(data)
  }
  
  # 4. 计算中性丢失（母离子 - mz）
  data <- data %>% mutate(Loss_Neutral = parent_ion - mz)
  
  # 5. 计算相对强度
  total_intensity <- sum(data$intensity, na.rm = TRUE)
  data <- data %>% mutate(relative_intensity = intensity / total_intensity)
  
  # 6. 计算熵值 E = -Sum(相对强度 * log(相对强度))
  valid_rel_int <- data$relative_intensity[data$relative_intensity > 0]
  if (length(valid_rel_int) > 0) {
    E_value <- -sum(valid_rel_int * log(valid_rel_int))
  } else {
    E_value <- 0
  }
  
  # 7. 输出到新文件
  E_rounded <- round(E_value, 4)
  output_file_name <- paste0(file_name, "_", E_rounded, ".xlsx")
  output_file_path <- file.path(output_dir, output_file_name)
  
  # 准备输出数据：处理后的第一个sheet + 其他原始sheet
  output_data <- list()
  output_data[[all_sheets[1]]] <- data

  # 如果有其他sheet，原样复制
  if (length(all_sheets) > 1) {
    for (i in 2:length(all_sheets)) {
      sheet_name <- all_sheets[i]
      other_sheet_data <- read_excel(file_path, sheet = i)
      output_data[[sheet_name]] <- other_sheet_data
    }
  }

  # 保存处理后的数据（包含所有sheet）
  write_xlsx(output_data, output_file_path)
  
  return(list(
    file_name = file_name,
    parent_ion = parent_ion,
    original_rows = original_rows,
    filtered_rows = filtered_rows,
    final_rows = final_rows,
    entropy = E_value,
    output_path = output_file_path,
    processing_time = Sys.time()
  ))
}

# 主函数：并行处理Excel文件
#' 并行处理Excel质谱数据文件
#'
#' @param input_dir 输入文件夹路径
#' @param output_dir 输出文件夹路径
#' @param num_cores 使用的CPU核心数，默认为NULL（自动检测）
#' @param verbose 是否显示详细信息，默认为TRUE
#' @return 处理结果列表
process_excel_parallel <- function(input_dir = "input",
                                  output_dir = "output",
                                  num_cores = NULL,
                                  verbose = TRUE) {

  if (verbose) {
    cat("=== Excel文件并行处理程序 ===\n")
    cat("开始时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
    cat("输入文件夹:", input_dir, "\n")
    cat("输出文件夹:", output_dir, "\n\n")
  }

  # 创建输出文件夹
  if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
    if (verbose) cat("✅ 创建输出文件夹:", output_dir, "\n")
  }

  # 扫描输入文件
  excel_files <- list.files(input_dir, pattern = "\\.xlsx?$", full.names = TRUE)

  if (length(excel_files) == 0) {
    if (verbose) cat("❌ 在", input_dir, "文件夹中没有找到Excel文件\n")
    return(list(
      success = FALSE,
      message = "没有找到Excel文件",
      files_processed = 0,
      results = list()
    ))
  }

  if (verbose) {
    cat("📁 找到", length(excel_files), "个Excel文件:\n")
    for (i in seq_along(excel_files)) {
      cat("  ", i, ".", basename(excel_files[i]), "\n")
    }
    cat("\n")
  }

  # 设置并行处理
  total_cores <- detectCores()
  if (is.null(num_cores)) {
    use_cores <- max(1, min(total_cores - 1, length(excel_files)))
  } else {
    use_cores <- max(1, min(num_cores, total_cores, length(excel_files)))
  }

  if (verbose) {
    cat("💻 检测到", total_cores, "个CPU核心\n")
    cat("🚀 将使用", use_cores, "个核心进行并行处理\n\n")
  }

  # 开始并行处理
  if (verbose) cat("⏱️  开始并行处理...\n")
  start_time <- Sys.time()

  # 设置并行后端
  cl <- makeCluster(use_cores)
  registerDoParallel(cl)

  # 并行处理所有文件
  results <- foreach(file_path = excel_files,
                    .packages = c("readxl", "dplyr", "writexl"),
                    .errorhandling = "pass",
                    .export = c("process_excel_file")) %dopar% {
    tryCatch({
      process_excel_file(file_path, output_dir)
    }, error = function(e) {
      list(error = TRUE, file = file_path, message = e$message)
    })
  }

  # 停止并行后端
  stopCluster(cl)

  end_time <- Sys.time()
  processing_time <- end_time - start_time

  if (verbose) {
    cat("✅ 并行处理完成！\n")
    cat("⏱️  总耗时:", round(processing_time, 2), attr(processing_time, "units"), "\n\n")
  }

  # 统计结果
  success_count <- 0
  error_count <- 0
  successful_results <- list()
  error_results <- list()

  for (i in seq_along(results)) {
    result <- results[[i]]
    if (!is.null(result$error) && result$error) {
      error_count <- error_count + 1
      error_results[[length(error_results) + 1]] <- result
      if (verbose) {
        cat("❌ 文件", i, ":", basename(result$file), "\n")
        cat("   错误:", result$message, "\n\n")
      }
    } else {
      success_count <- success_count + 1
      successful_results[[length(successful_results) + 1]] <- result
      if (verbose) {
        cat("✅ 文件", i, ":", result$file_name, "\n")
        cat("   母离子质量:", result$parent_ion, "\n")
        cat("   数据行数: 原始", result$original_rows, "→ 过滤", result$filtered_rows, "→ 最终", result$final_rows, "\n")
        cat("   熵值 E:", round(result$entropy, 6), "\n")
        cat("   输出文件:", basename(result$output_path), "\n\n")
      }
    }
  }

  # 输出统计信息
  if (verbose) {
    cat("=== 📈 处理统计 ===\n")
    cat("总文件数:", length(excel_files), "\n")
    cat("成功处理:", success_count, "\n")
    cat("处理失败:", error_count, "\n")
    cat("成功率:", round(success_count/length(excel_files)*100, 1), "%\n")
    cat("总处理时间:", round(processing_time, 2), attr(processing_time, "units"), "\n")
    if (success_count > 0) {
      avg_time <- as.numeric(processing_time) / success_count
      cat("平均每文件:", round(avg_time, 2), attr(processing_time, "units"), "\n")
    }
    cat("\n🎉 程序执行完成！\n")
    cat("结束时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
  }

  # 返回处理结果
  return(list(
    success = success_count > 0,
    total_files = length(excel_files),
    success_count = success_count,
    error_count = error_count,
    success_rate = round(success_count/length(excel_files)*100, 1),
    processing_time = processing_time,
    successful_results = successful_results,
    error_results = error_results,
    input_dir = input_dir,
    output_dir = output_dir,
    cores_used = use_cores
  ))
}
