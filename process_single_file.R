# 处理单个Excel文件的简化脚本
# 专门处理 1_60_375.29.xlsx 文件

# 加载必要的包
library(readxl)
library(dplyr)
library(writexl)

# 设置工作目录
setwd("C:/Users/<USER>/Desktop/test")

# 创建输出文件夹
if (!dir.exists("output")) {
  dir.create("output")
}

# 文件路径
input_file <- "input/1_60_375.29.xlsx"
file_name <- "1_60_375.29"

# 从文件名提取母离子质量：375.29
parent_ion <- 375.29

cat("处理文件:", input_file, "\n")
cat("母离子质量:", parent_ion, "\n")

# 读取Excel文件
data <- read_excel(input_file)

# 转换数据类型
data$mz <- as.numeric(data$mz)
data$intensity <- as.numeric(data$intensity)

# 检查转换后是否有NA值
if (any(is.na(data$mz))) {
  cat("警告: mz列中有无法转换的值\n")
}
if (any(is.na(data$intensity))) {
  cat("警告: intensity列中有无法转换的值\n")
}

# 显示原始数据信息
cat("原始数据行数:", nrow(data), "\n")
cat("原始数据列名:", paste(colnames(data), collapse = ", "), "\n")
cat("mz列数据类型:", class(data$mz), "\n")
cat("intensity列数据类型:", class(data$intensity), "\n")

# 1. 按mz列降序排列
data <- data %>% arrange(desc(mz))

# 2. 删除mz > 母离子+0.005的行
threshold <- parent_ion + 0.005
cat("过滤阈值 (母离子+0.005):", threshold, "\n")
data <- data %>% filter(mz <= threshold)

cat("过滤后数据行数:", nrow(data), "\n")

# 3. 保留前60行（如果少于60行则全部保留）
if (nrow(data) > 60) {
  data <- data %>% slice_head(n = 60)
  cat("保留前60行\n")
} else {
  cat("数据少于60行，全部保留:", nrow(data), "行\n")
}

# 4. 计算中性丢失（母离子 - mz）
data <- data %>% mutate(Loss_Neutral = parent_ion - mz)

# 5. 计算相对强度
total_intensity <- sum(data$intensity, na.rm = TRUE)
data <- data %>% mutate(relative_intensity = intensity / total_intensity)

cat("总强度:", total_intensity, "\n")

# 6. 计算熵值 E = -Sum(相对强度 * log(相对强度))
# 使用自然对数，过滤掉0值避免log(0)的问题
valid_rel_int <- data$relative_intensity[data$relative_intensity > 0]
if (length(valid_rel_int) > 0) {
  E_value <- -sum(valid_rel_int * log(valid_rel_int))
} else {
  E_value <- 0
}

cat("计算得到的熵值 E:", E_value, "\n")

# 添加熵值列
data <- data %>% mutate(Entropy_E = E_value)

# 7. 输出到新文件
# 将E值四舍五入到4位小数，避免文件名过长
E_rounded <- round(E_value, 4)
output_file <- paste0("output/", file_name, "_", E_rounded, ".xlsx")
write_xlsx(data, output_file)

cat("文件已保存到:", output_file, "\n")
cat("最终数据行数:", nrow(data), "\n")
cat("最终数据列名:", paste(colnames(data), collapse = ", "), "\n")

# 显示前几行数据预览
cat("\n前5行数据预览:\n")
print(head(data, 5))

cat("\n脚本执行完成！\n")
