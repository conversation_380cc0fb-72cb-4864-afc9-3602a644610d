# Excel质谱数据处理脚本

## 功能说明

这个R脚本用于处理质谱数据Excel文件，执行以下操作：

1. **按mz列降序排列**
2. **过滤数据**：删除mz > 母离子+0.005的行
3. **保留前60行**：如果数据少于60行则全部保留
4. **计算中性丢失**：母离子 - mz，新增Loss_Neutral列
5. **计算相对强度**：intensity/sum(intensity)，新增relative_intensity列
6. **计算熵值E**：-Sum(相对强度*log(相对强度))
7. **输出处理后的文件**：文件名为原文件名_E

## 文件说明

- `process_excel.R`: 通用脚本，可以处理input文件夹中的所有Excel文件
- `process_single_file.R`: 专门处理单个文件的脚本
- `README.md`: 本说明文档

## 使用要求

### R包依赖
```r
install.packages(c("readxl", "dplyr", "writexl"))
```

### 文件名格式
Excel文件名必须包含至少两个下划线，母离子质量在第二个下划线后面。
例如：`1_60_375.29.xlsx` → 母离子质量为375.29

### 数据格式
Excel文件必须包含以下列：
- `mz`: 质荷比
- `intensity`: 强度

## 使用方法

### 方法1：处理单个文件
```bash
Rscript process_single_file.R
```

### 方法2：批量处理
```bash
Rscript process_excel.R
```

## 输出结果

处理后的文件将保存在`output`文件夹中，包含以下列：
- `mz`: 原始质荷比
- `intensity`: 原始强度
- `Loss_Neutral`: 中性丢失（母离子 - mz）
- `relative_intensity`: 相对强度
- `Entropy_E`: 熵值E

## 处理示例

对于文件`1_60_375.29.xlsx`：
- 母离子质量：375.29
- 过滤阈值：375.295 (375.29 + 0.005)
- 原始数据：88行
- 过滤后：87行
- 最终保留：60行
- 计算熵值：3.401093
- 输出文件：`output/1_60_375.29_E.xlsx`

## 注意事项

1. 脚本会自动创建`output`文件夹
2. 如果Excel中的数据是文本格式，脚本会自动转换为数值格式
3. 熵值计算使用自然对数（ln），如需要其他底数可修改脚本
4. 相对强度为0的数据在计算熵值时会被过滤掉，避免log(0)的问题

## 错误处理

- 如果文件名格式不正确，脚本会报错并停止
- 如果缺少必要的列（mz或intensity），脚本会报错
- 如果数据无法转换为数值，会显示警告信息
